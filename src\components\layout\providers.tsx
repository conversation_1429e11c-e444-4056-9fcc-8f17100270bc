"use client";

import React from "react";
import { ActiveThemeProvider } from "@/components/active-theme";
import { AuthProvider } from "@/features/auth/context/auth-context";

export default function Providers({
  activeThemeValue,
  children,
}: {
  activeThemeValue: string;
  children: React.ReactNode;
}) {
  return (
    <>
      <ActiveThemeProvider initialTheme={activeThemeValue}>
        <AuthProvider>{children}</AuthProvider>
      </ActiveThemeProvider>
    </>
  );
}
