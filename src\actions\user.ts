"use server";

import { prisma as db } from "@/lib/db";
import { verifyAdmin } from "@/lib/auth";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import { ActionResult } from "@/types";
import {
  createUserSchema,
  deleteUserSchema,
  updateUserSchema,
} from "@/lib/validation";

export async function createUser(formData: FormData): Promise<ActionResult> {
  try {
    const { isAuthenticated, isAdmin } = await verifyAdmin();

    if (!isAuthenticated) {
      return { success: false, error: "Authentication required" };
    }

    if (!isAdmin) {
      return { success: false, error: "Admin privileges required" };
    }

    const validatedData = createUserSchema.parse({
      firebaseUid: formData.get("firebaseUid")?.toString(),
      email: formData.get("email")?.toString(),
      name: formData.get("name")?.toString(),
      imageUrl: formData.get("imageUrl")?.toString() || undefined,
      role: formData.get("role")?.toString() || "USER",
    });

    const existingUser = await db.user.findFirst({
      where: {
        OR: [
          { firebaseUid: validatedData.firebaseUid },
          { email: validatedData.email },
        ],
      },
    });

    if (existingUser) {
      return {
        success: false,
        error: "User already exists with this Firebase UID or email",
      };
    }

    const user = await db.user.create({
      data: {
        firebaseUid: validatedData.firebaseUid,
        email: validatedData.email,
        name: validatedData.name,
        imageUrl: validatedData.imageUrl,
        role: validatedData.role,
        lastLogin: new Date(),
      },
      select: {
        id: true,
        firebaseUid: true,
        email: true,
        name: true,
        imageUrl: true,
        role: true,
        createdAt: true,
        onboardingCompleted: true,
      },
    });

    await db.auditLog.create({
      data: {
        userId: user.id,
        action: "USER_CREATED",
        details: `User created: ${user.email}`,
      },
    });

    revalidatePath("/dashboard/users");
    return { success: true, data: user };
  } catch (error) {
    console.error("Error creating user:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map((e) => e.message).join(", "),
      };
    }

    return {
      success: false,
      error: "Failed to create user. Please try again.",
    };
  }
}

export async function updateUser(formData: FormData): Promise<ActionResult> {
  try {
    const { isAuthenticated, isAdmin } = await verifyAdmin();

    if (!isAuthenticated) {
      return { success: false, error: "Authentication required" };
    }

    if (!isAdmin) {
      return { success: false, error: "Admin privileges required" };
    }

    // Validate input data
    const validatedData = updateUserSchema.parse({
      id: formData.get("id")?.toString(),
      name: formData.get("name")?.toString() || undefined,
      email: formData.get("email")?.toString() || undefined,
      imageUrl: formData.get("imageUrl")?.toString() || undefined,
      role: formData.get("role")?.toString() || undefined,
      onboardingCompleted:
        formData.get("onboardingCompleted") === "true" || undefined,
    });

    const existingUser = await db.user.findUnique({
      where: { id: validatedData.id },
    });

    if (!existingUser) {
      return { success: false, error: "User not found" };
    }

    // If email is being updated, check for conflicts
    if (validatedData.email && validatedData.email !== existingUser.email) {
      const emailConflict = await db.user.findFirst({
        where: {
          email: validatedData.email,
          id: { not: validatedData.id },
        },
      });

      if (emailConflict) {
        return {
          success: false,
          error: "Email already in use by another user",
        };
      }
    }

    // Update the user
    const updatedUser = await db.user.update({
      where: { id: validatedData.id },
      data: {
        ...(validatedData.name && { name: validatedData.name }),
        ...(validatedData.email && { email: validatedData.email }),
        ...(validatedData.imageUrl !== undefined && {
          imageUrl: validatedData.imageUrl,
        }),
        ...(validatedData.role && { role: validatedData.role }),
        ...(validatedData.onboardingCompleted !== undefined && {
          onboardingCompleted: validatedData.onboardingCompleted,
        }),
        updatedAt: new Date(),
      },
      select: {
        id: true,
        firebaseUid: true,
        email: true,
        name: true,
        imageUrl: true,
        role: true,
        createdAt: true,
        updatedAt: true,
        onboardingCompleted: true,
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        userId: updatedUser.id,
        action: "USER_UPDATED",
        details: `User updated: ${updatedUser.email}`,
      },
    });

    revalidatePath("/dashboard/users");
    return { success: true, data: updatedUser };
  } catch (error) {
    console.error("Error updating user:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map((e) => e.message).join(", "),
      };
    }

    return {
      success: false,
      error: "Failed to update user. Please try again.",
    };
  }
}

export async function deleteUser(formData: FormData): Promise<ActionResult> {
  try {
    const { isAuthenticated, isAdmin } = await verifyAdmin();

    if (!isAuthenticated) {
      return { success: false, error: "Authentication required" };
    }

    if (!isAdmin) {
      return { success: false, error: "Admin privileges required" };
    }

    const validatedData = deleteUserSchema.parse({
      id: formData.get("id")?.toString(),
    });

    const existingUser = await db.user.findUnique({
      where: { id: validatedData.id },
      select: { id: true, email: true },
    });

    if (!existingUser) {
      return { success: false, error: "User not found" };
    }

    await db.user.delete({
      where: { id: validatedData.id },
    });

    revalidatePath("/dashboard/users");
    return {
      success: true,
      data: { message: `User ${existingUser.email} deleted successfully` },
    };
  } catch (error) {
    console.error("Error deleting user:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map((e) => e.message).join(", "),
      };
    }

    return {
      success: false,
      error: "Failed to delete user. Please try again.",
    };
  }
}

export async function getUsers(): Promise<ActionResult> {
  try {
    // Verify admin permissions
    const { isAuthenticated, isAdmin } = await verifyAdmin();

    if (!isAuthenticated) {
      return { success: false, error: "Authentication required" };
    }

    if (!isAdmin) {
      return { success: false, error: "Admin privileges required" };
    }

    const users = await db.user.findMany({
      select: {
        id: true,
        firebaseUid: true,
        email: true,
        name: true,
        imageUrl: true,
        role: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true,
        onboardingCompleted: true,
        _count: {
          select: {
            auditLogs: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    return { success: true, data: users };
  } catch (error) {
    console.error("Error fetching users:", error);
    return {
      success: false,
      error: "Failed to fetch users. Please try again.",
    };
  }
}

export async function getUserById(userId: string): Promise<ActionResult> {
  try {
    // Verify admin permissions
    const { isAuthenticated, isAdmin } = await verifyAdmin();

    if (!isAuthenticated) {
      return { success: false, error: "Authentication required" };
    }

    if (!isAdmin) {
      return { success: false, error: "Admin privileges required" };
    }

    if (!userId) {
      return { success: false, error: "User ID is required" };
    }

    const user = await db.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        firebaseUid: true,
        email: true,
        name: true,
        imageUrl: true,
        role: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true,
        onboardingCompleted: true,
        auditLogs: {
          select: {
            id: true,
            action: true,
            details: true,
            timestamp: true,
            ipAddress: true,
            userAgent: true,
          },
          orderBy: {
            timestamp: "desc",
          },
          take: 10,
        },
      },
    });

    if (!user) {
      return { success: false, error: "User not found" };
    }

    return { success: true, data: user };
  } catch (error) {
    console.error("Error fetching user:", error);
    return {
      success: false,
      error: "Failed to fetch user. Please try again.",
    };
  }
}

export async function updateLastLogin(
  firebaseUid: string
): Promise<ActionResult> {
  try {
    if (!firebaseUid) {
      return { success: false, error: "Firebase UID is required" };
    }

    const user = await db.user.update({
      where: { firebaseUid },
      data: { lastLogin: new Date() },
      select: { id: true, email: true, lastLogin: true },
    });

    return { success: true, data: user };
  } catch (error) {
    console.error("Error updating last login:", error);
    return {
      success: false,
      error: "Failed to update last login.",
    };
  }
}

export async function completeOnboarding(
  firebaseUid: string
): Promise<ActionResult> {
  try {
    if (!firebaseUid) {
      return { success: false, error: "Firebase UID is required" };
    }

    const user = await db.user.update({
      where: { firebaseUid },
      data: { onboardingCompleted: true },
      select: {
        id: true,
        email: true,
        onboardingCompleted: true,
        name: true,
        imageUrl: true,
      },
    });

    // Create audit log
    await db.auditLog.create({
      data: {
        userId: user.id,
        action: "ONBOARDING_COMPLETED",
        details: `User completed onboarding: ${user.email}`,
      },
    });

    revalidatePath("/dashboard");
    return { success: true, data: user };
  } catch (error) {
    console.error("Error completing onboarding:", error);
    return {
      success: false,
      error: "Failed to complete onboarding.",
    };
  }
}

export async function getOrCreateUser(userData: {
  firebaseUid: string;
  email: string;
  name?: string;
  imageUrl?: string;
}): Promise<ActionResult> {
  try {
    const validatedData = createUserSchema.parse({
      firebaseUid: userData.firebaseUid,
      email: userData.email,
      name: userData.name || userData.email.split("@")[0],
      imageUrl: userData.imageUrl || "",
      role: "USER",
    });

    let user = await db.user.findUnique({
      where: { firebaseUid: validatedData.firebaseUid },
      select: {
        id: true,
        firebaseUid: true,
        email: true,
        name: true,
        imageUrl: true,
        role: true,
        createdAt: true,
        onboardingCompleted: true,
        lastLogin: true,
      },
    });

    if (user) {
      user = await db.user.update({
        where: { firebaseUid: validatedData.firebaseUid },
        data: { lastLogin: new Date() },
        select: {
          id: true,
          firebaseUid: true,
          email: true,
          name: true,
          imageUrl: true,
          role: true,
          createdAt: true,
          onboardingCompleted: true,
          lastLogin: true,
        },
      });
    } else {
      user = await db.user.create({
        data: {
          firebaseUid: validatedData.firebaseUid,
          email: validatedData.email,
          name: validatedData.name,
          imageUrl: validatedData.imageUrl,
          role: validatedData.role,
          lastLogin: new Date(),
        },
        select: {
          id: true,
          firebaseUid: true,
          email: true,
          name: true,
          imageUrl: true,
          role: true,
          createdAt: true,
          onboardingCompleted: true,
          lastLogin: true,
        },
      });

      await db.auditLog.create({
        data: {
          userId: user.id,
          action: "USER_REGISTERED",
          details: `New user registered: ${user.email}`,
        },
      });
    }

    return { success: true, data: user };
  } catch (error) {
    console.error("Error getting or creating user:", error);

    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map((e) => e.message).join(", "),
      };
    }

    return {
      success: false,
      error: "Failed to get or create user.",
    };
  }
}
