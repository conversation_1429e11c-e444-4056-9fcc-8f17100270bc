import { NextResponse } from "next/server";
import { auth as adminAuth } from "@/firebase/admin";
import { prisma as db } from "@/lib/db";

export async function POST(request: Request) {
  try {
    const { idToken } = await request.json();
    if (!idToken) {
      return NextResponse.json({ error: "Missing idToken" }, { status: 400 });
    }

    // Verify ID token and create session cookie
    const decoded = await adminAuth.verifyIdToken(idToken);
    const fiveDays = 5 * 24 * 60 * 60 * 1000;
    const sessionCookie = await adminAuth.createSessionCookie(idToken, {
      expiresIn: fiveDays,
    });

    // Upsert user in Neon
    const { uid, email, name, picture } = {
      uid: decoded.uid,
      email: decoded.email ?? "",
      name: (decoded as any).name ?? null,
      picture: (decoded as any).picture ?? null,
    };

    if (!email) {
      return NextResponse.json(
        { error: "Email is required on Firebase token" },
        { status: 400 }
      );
    }

    await db.user.upsert({
      where: { firebaseUid: uid },
      update: {
        email,
        name,
        imageUrl: picture ?? undefined,
        lastLogin: new Date(),
      },
      create: {
        firebaseUid: uid,
        email,
        name,
        imageUrl: picture ?? undefined,
        lastLogin: new Date(),
      },
    });

    const res = NextResponse.json({ success: true });
    res.cookies.set("session", sessionCookie, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
      maxAge: fiveDays / 1000,
    });
    return res;
  } catch (e) {
    console.error("/api/session error", e);
    return NextResponse.json(
      { error: "Failed to create session" },
      { status: 500 }
    );
  }
}
