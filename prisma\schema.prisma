// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum Role {
  ADMIN
  USER
}

model User {
  id                  String     @id @default(cuid())
  firebaseUid         String     @unique
  email               String     @unique
  name                String?
  imageUrl            String?
  lastLogin           DateTime?
  createdAt           DateTime   @default(now())
  updatedAt           DateTime   @updatedAt
  role                Role       @default(USER)
  auditLogs           AuditLog[]
  onboardingCompleted Boolean    @default(false)
}

model AuditLog {
  id        String   @id @default(cuid())
  userId    String
  action    String
  details   String?
  ipAddress String?
  userAgent String?
  timestamp DateTime @default(now())

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([timestamp])
}
