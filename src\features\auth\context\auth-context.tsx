"use client";

import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { auth, googleProvider } from "@/firebase/client";
import {
  onIdTokenChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signInWithPopup,
  signOut as firebaseSignOut,
  updateProfile,
} from "firebase/auth";

// Types for AuthContext
export type AuthUser = {
  uid: string;
  email: string | null;
  name?: string | null;
  imageUrl?: string | null;
};

type AuthContextValue = {
  user: AuthUser | null;
  loading: boolean;
  signUpWithEmail: (
    email: string,
    password: string,
    name?: string
  ) => Promise<void>;
  signInWithEmail: (email: string, password: string) => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
};

const AuthContext = createContext<AuthContextValue | undefined>(undefined);

async function createSession(idToken: string) {
  await fetch("/api/session", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ idToken }),
  });
}

async function clearSession() {
  await fetch("/api/logout", { method: "POST" });
}

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [loading, setLoading] = useState(true);

  // Whenever Firebase ID token changes, refresh server session cookie and sync user in DB
  useEffect(() => {
    const unsub = onIdTokenChanged(auth, async (fbUser) => {
      try {
        if (!fbUser) {
          setUser(null);
          await clearSession();
          setLoading(false);
          return;
        }
        const idToken = await fbUser.getIdToken();
        await createSession(idToken);
        setUser({
          uid: fbUser.uid,
          email: fbUser.email,
          name: fbUser.displayName,
          imageUrl: fbUser.photoURL,
        });
      } catch (e) {
        // eslint-disable-next-line no-console
        console.error("Failed to refresh session:", e);
      } finally {
        setLoading(false);
      }
    });
    return () => unsub();
  }, []);

  const signUpWithEmail = useCallback(
    async (email: string, password: string, name?: string) => {
      const cred = await createUserWithEmailAndPassword(auth, email, password);
      if (name) {
        // Update profile displayName for consistency
        await updateProfile(cred.user, { displayName: name });
      }
      // onIdTokenChanged handler will set session and sync DB
    },
    []
  );

  const signInWithEmail = useCallback(
    async (email: string, password: string) => {
      await signInWithEmailAndPassword(auth, email, password);
      // onIdTokenChanged handler will set session and sync DB
    },
    []
  );

  const signInWithGoogle = useCallback(async () => {
    await signInWithPopup(auth, googleProvider);
    // onIdTokenChanged handler will set session and sync DB
  }, []);

  const signOut = useCallback(async () => {
    await firebaseSignOut(auth);
    await clearSession();
    setUser(null);
  }, []);

  const value = useMemo(
    () => ({
      user,
      loading,
      signInWithEmail,
      signUpWithEmail,
      signInWithGoogle,
      signOut,
    }),
    [user, loading, signInWithEmail, signUpWithEmail, signInWithGoogle, signOut]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export function useAuth() {
  const ctx = useContext(AuthContext);
  if (!ctx) throw new Error("useAuth must be used within AuthProvider");
  return ctx;
}
