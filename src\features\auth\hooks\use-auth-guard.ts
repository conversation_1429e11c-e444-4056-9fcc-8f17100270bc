"use client";

import { useAuth } from "@/features/auth/context/auth-context";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

interface UseAuthGuardOptions {
  redirectTo?: string;
  requireAuth?: boolean;
}

export function useAuthGuard(options: UseAuthGuardOptions = {}) {
  const { redirectTo = "/auth/sign-in", requireAuth = true } = options;
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading) {
      if (requireAuth && !user) {
        router.push(redirectTo);
      } else if (!requireAuth && user) {
        // Redirect authenticated users away from auth pages
        router.push("/dashboard/overview");
      }
    }
  }, [user, loading, router, redirectTo, requireAuth]);

  return {
    user,
    loading,
    isAuthenticated: !!user,
  };
}
