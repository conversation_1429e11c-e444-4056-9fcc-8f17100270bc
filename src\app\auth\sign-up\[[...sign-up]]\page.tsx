import { Metadata } from "next";
import SignUpForm from "@/features/auth/components/form/sign-up-form";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Authentication | Sign Up",
  description: "Sign Up page for authentication.",
};

export default async function Page() {
  return (
    <>
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-2xl font-bold">Create an account</h1>
        <p className="text-muted-foreground text-sm text-balance">
          Enter your details below to create your account
        </p>
      </div>
      <SignUpForm />
      <div className="text-center text-sm">
        Already have an account?{" "}
        <Link href="/auth/sign-in" className="underline hover:text-primary/80">
          Sign in
        </Link>
      </div>
    </>
  );
}
