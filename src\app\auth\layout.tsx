import { Brain } from "lucide-react";
import Link from "next/link";

export default function layout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <div className="relative h-screen flex-col items-center justify-center md:grid lg:max-w-none lg:grid-cols-2 lg:px-0">
        <div className="bg-muted relative hidden h-full flex-col p-10 lg:flex dark:border-r">
          <div className="absolute inset-0" />
          <div className="relative z-20 flex items-center text-lg font-medium gap-2">
            <Link href="/" className="flex items-center gap-2">
              <div className="bg-primary text-primary-foreground flex p-1.5 items-center justify-center rounded-md">
                <Brain className="size-4" />
              </div>
              <span>AI Interview Prep</span>
            </Link>
          </div>

          <div className="relative z-20 mt-auto">
            <blockquote className="space-y-2">
              <p className="text-lg">
                The authentication system is seamless and the developer
                experience is outstanding. Highly recommended!
              </p>
              <footer className="text-sm">Happy Developer</footer>
            </blockquote>
          </div>
        </div>

        <div className="flex h-full items-center justify-center p-4 lg:p-8">
          <div className="flex w-full max-w-md flex-col items-center justify-center space-y-6">
            {children}
          </div>
        </div>
      </div>
    </>
  );
}
