"use client";

import React from "react";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { Breadcrumbs } from "@/components/breadcrumbs";
import { ThemeSelector } from "@/components/theme-selector";
import { ModeToggle } from "@/components/layout/ThemeToggle/theme-toggle";
import { UserDropdownMenu } from "@/components/layout/user-dropdown";
import { useUser } from "@clerk/nextjs";
import SearchInput from "@/components/layout/search-input";
import CtaGithub from "@/components/layout/cta-github";

export default function Header() {
  const { user, isLoaded } = useUser();
  return (
    <header className="flex h-16 shrink-0 items-center justify-between gap-2 transition-[width,height] ease-linear group-has-data-[collapsible=icon]/sidebar-wrapper:h-12">
      <div className="flex items-center gap-2 px-4">
        <SidebarTrigger className="-ml-1" />
        <Separator orientation="vertical" className="mr-2 h-4" />
        <Breadcrumbs />
      </div>

      <div className="flex items-center gap-2 px-4">
        <CtaGithub />
        <div className="hidden md:flex">
          <SearchInput />
        </div>
        <UserDropdownMenu user={user} isLoading={!isLoaded} size="md" />
        <ModeToggle />
        <div className="hidden md:flex">
          <ThemeSelector />
        </div>
      </div>
    </header>
  );
}
