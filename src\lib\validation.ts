import z from "zod";

// Validation schemas
export const createUserSchema = z.object({
  firebaseUid: z.string().min(1, "Firebase UID is required"),
  email: z.string().email("Invalid email address"),
  name: z.string().min(1, "Name is required").max(100, "Name is too long"),
  imageUrl: z.string().url().optional().or(z.literal("")),
  role: z.enum(["ADMIN", "USER"]).default("USER"),
});

export const updateUserSchema = z.object({
  id: z.string().min(1, "User ID is required"),
  name: z
    .string()
    .min(1, "Name is required")
    .max(100, "Name is too long")
    .optional(),
  email: z.string().email("Invalid email address").optional(),
  imageUrl: z.string().url().optional().or(z.literal("")),
  role: z.enum(["ADMIN", "USER"]).optional(),
  onboardingCompleted: z.boolean().optional(),
});

export const deleteUserSchema = z.object({
  id: z.string().min(1, "User ID is required"),
});
