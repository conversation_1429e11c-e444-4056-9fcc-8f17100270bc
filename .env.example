# =================================================================
# Authentication Configuration (Clerk)
# =================================================================
# IMPORTANT: This template supports <PERSON>'s keyless mode!
# You can start using the app immediately without any configuration.
# When you're ready to claim your application, simply click the Clerk
# popup at the bottom of the screen to get your API keys.

# Required: Clerk API Keys (Leave empty for keyless mode)

NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY=
CLERK_SECRET_KEY=

# =================================================================
# Authentication Redirect URLs
# =================================================================
# These control where users are directed after authentication actions

NEXT_PUBLIC_CLERK_SIGN_IN_URL="/auth/sign-in"
NEXT_PUBLIC_CLERK_SIGN_UP_URL="/auth/sign-up"
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/dashboard/overview"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/dashboard/overview"

# =================================================================
# Important Notes:
# =================================================================
# 1. Rename this file to '.env' for local development
# 2. Never commit the actual '.env' file to version control
# 3. Make sure to replace all placeholder values with real ones
# 4. Keep your secret keys private and never share them
