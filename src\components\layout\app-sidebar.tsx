"use client";

import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar";
import { navItems } from "@/constants/data";
import { useUser } from "@clerk/nextjs";
import { usePathname } from "next/navigation";
import * as React from "react";
import { SidebarLogo } from "@/components/layout/sidebar-logo";
import { UserDropdownMenu } from "@/components/layout/user-dropdown";
import { NavMain } from "@/components/layout/nav-main";

export default function AppSidebar() {
  const pathname = usePathname();
  const { user, isLoaded } = useUser();

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        <SidebarLogo />
      </SidebarHeader>
      <SidebarContent className="overflow-x-hidden">
        <NavMain navItems={navItems} />
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <UserDropdownMenu
              user={user}
              isLoading={!isLoaded}
              variant="sidebar"
            />
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
