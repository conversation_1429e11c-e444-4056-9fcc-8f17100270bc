"use client";

import {
  Sidebar,
  <PERSON>barContent,
  <PERSON>bar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar";
import { navItems } from "@/constants/data";
import { useAuth } from "@/features/auth/context/auth-context";

import * as React from "react";
import { SidebarLogo } from "@/components/layout/sidebar-logo";
import { UserDropdownMenu } from "@/components/layout/user-dropdown";
import { NavMain } from "@/components/layout/nav-main";

export default function AppSidebar() {
  const { user, loading } = useAuth();

  // Transform Firebase user to match expected format
  const transformedUser = user
    ? {
        imageUrl: user.imageUrl,
        fullName: user.name,
        emailAddresses: [{ emailAddress: user.email || "" }],
      }
    : null;

  return (
    <Sidebar collapsible="icon">
      <SidebarHeader>
        <SidebarLogo />
      </SidebarHeader>
      <SidebarContent className="overflow-x-hidden">
        <NavMain navItems={navItems} />
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <UserDropdownMenu
              user={transformedUser}
              isLoading={loading}
              variant="sidebar"
            />
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
